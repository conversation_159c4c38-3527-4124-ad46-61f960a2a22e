package com.light.aiszzy.homework.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.light.aiszzy.homework.entity.dto.HomeworkClassDto;
import com.light.aiszzy.homework.entity.bo.HomeworkClassConditionBo;
import com.light.aiszzy.homework.entity.bo.HomeworkClassBo;
import com.light.aiszzy.homework.entity.vo.HomeworkClassVo;
import com.light.core.entity.AjaxResult;

import java.util.List;

/**
 * 作业班级信息，包含疑问项汇总接口
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-22 17:01:24
 */
public interface IHomeworkClassService extends IService<HomeworkClassDto> {

    List<HomeworkClassVo> getHomeworkClassListByCondition(HomeworkClassConditionBo condition);

	AjaxResult addHomeworkClass(HomeworkClassBo HomeworkClassBo);

	AjaxResult updateHomeworkClass(HomeworkClassBo HomeworkClassBo);

    HomeworkClassVo getDetail(String oid);

    HomeworkClassVo getDetailByHomeworkClassId(String homeworOid,Long classId);

}

