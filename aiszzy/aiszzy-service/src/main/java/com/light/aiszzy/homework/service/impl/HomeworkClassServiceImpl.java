package com.light.aiszzy.homework.service.impl;

import cn.hutool.core.util.IdUtil;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.List;

import com.light.core.enums.StatusEnum;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import javax.annotation.Resource;

import com.light.aiszzy.homework.entity.dto.HomeworkClassDto;
import com.light.aiszzy.homework.entity.bo.HomeworkClassConditionBo;
import com.light.aiszzy.homework.entity.bo.HomeworkClassBo;
import com.light.aiszzy.homework.entity.vo.HomeworkClassVo;
import com.light.aiszzy.homework.service.IHomeworkClassService;
import com.light.aiszzy.homework.mapper.HomeworkClassMapper;
import com.light.core.entity.AjaxResult;
/**
 * 作业班级信息，包含疑问项汇总接口实现类
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-22 17:01:24
 */
@Service
public class HomeworkClassServiceImpl extends ServiceImpl<HomeworkClassMapper, HomeworkClassDto> implements IHomeworkClassService {

	@Resource
	private HomeworkClassMapper HomeworkClassMapper;
	
    @Override
	public List<HomeworkClassVo> getHomeworkClassListByCondition(HomeworkClassConditionBo condition) {
        return HomeworkClassMapper.getHomeworkClassListByCondition(condition);
	}

	@Override
	public AjaxResult addHomeworkClass(HomeworkClassBo HomeworkClassBo) {
		HomeworkClassDto HomeworkClass = new HomeworkClassDto();
		BeanUtils.copyProperties(HomeworkClassBo, HomeworkClass);
		HomeworkClass.setOid(IdUtil.simpleUUID());
		if(save(HomeworkClass)){
			return AjaxResult.success("保存成功");
		}else{
			return AjaxResult.fail("保存失败");
		}
	}

	@Override
	public AjaxResult updateHomeworkClass(HomeworkClassBo HomeworkClassBo) {
		LambdaQueryWrapper<HomeworkClassDto> lqw = new LambdaQueryWrapper<>();
		lqw.eq(HomeworkClassDto::getOid, HomeworkClassBo.getOid());
		HomeworkClassDto HomeworkClass = getOne(lqw);
		Long id = HomeworkClass.getId();
		if(HomeworkClass == null){
			return AjaxResult.fail("保存失败");
		}
		BeanUtils.copyProperties(HomeworkClassBo, HomeworkClass);
		HomeworkClass.setId(id);
		if(updateById(HomeworkClass)){
			return AjaxResult.success("保存成功");
		}else{
			return AjaxResult.fail("保存失败");
		}
	}

	@Override
	public HomeworkClassVo getDetail(String oid) {
		LambdaQueryWrapper<HomeworkClassDto> lqw = new LambdaQueryWrapper<>();
		lqw.eq(HomeworkClassDto::getOid, oid);
		HomeworkClassDto HomeworkClass = getOne(lqw);
	    HomeworkClassVo HomeworkClassVo = new HomeworkClassVo();
		if(HomeworkClass != null){
			BeanUtils.copyProperties(HomeworkClass, HomeworkClassVo);
		}
		return HomeworkClassVo;
	}

	@Override
	public HomeworkClassVo getDetailByHomeworkClassId(String homeworkOid, Long classId) {
		LambdaQueryWrapper<HomeworkClassDto> lqw = new LambdaQueryWrapper<>();
		lqw.eq(HomeworkClassDto::getHomeworkOid, homeworkOid);
		lqw.eq(HomeworkClassDto::getClassId, classId);
		HomeworkClassDto HomeworkClass = getOne(lqw);
		HomeworkClassVo HomeworkClassVo = new HomeworkClassVo();
		if(HomeworkClass != null){
			BeanUtils.copyProperties(HomeworkClass, HomeworkClassVo);
		}
		return HomeworkClassVo;
	}

}