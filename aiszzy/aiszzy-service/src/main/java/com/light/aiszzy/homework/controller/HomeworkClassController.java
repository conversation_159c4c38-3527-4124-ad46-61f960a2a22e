package com.light.aiszzy.homework.controller;

import java.util.List;

import javax.validation.constraints.NotNull;

import io.swagger.annotations.Api;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import com.light.aiszzy.homework.entity.bo.HomeworkClassConditionBo;
import com.light.aiszzy.homework.entity.bo.HomeworkClassBo;
import com.light.aiszzy.homework.entity.vo.HomeworkClassVo;
import com.light.aiszzy.homework.service.IHomeworkClassService;

import com.light.aiszzy.homework.api.HomeworkClassApi;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.light.core.entity.AjaxResult;
import com.light.core.enums.StatusEnum;

/**
 * 作业班级信息，包含疑问项汇总
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-22 17:01:24
 */
@RestController
@Validated
@Api(value = "", tags = "作业班级信息，包含疑问项汇总接口")
public class HomeworkClassController implements HomeworkClassApi {

    @Autowired
    private IHomeworkClassService HomeworkClassService;

    public AjaxResult<PageInfo<HomeworkClassVo>> getHomeworkClassPageListByCondition(@RequestBody HomeworkClassConditionBo condition) {
        condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
        PageHelper.startPage(condition.getPageNo(), condition.getPageSize(), condition.getOrderBy());
        PageInfo<HomeworkClassVo> pageInfo = new PageInfo<>(HomeworkClassService.getHomeworkClassListByCondition(condition));
        return AjaxResult.success(pageInfo.getList(), pageInfo.getTotal(), condition.getPageNo(), condition.getPageSize());
    }

    public AjaxResult<List<HomeworkClassVo>> getHomeworkClassListByCondition(@RequestBody HomeworkClassConditionBo condition) {
        condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
        return AjaxResult.success(HomeworkClassService.getHomeworkClassListByCondition(condition));
    }

    public AjaxResult addHomeworkClass(@Validated @RequestBody HomeworkClassBo HomeworkClassBo) {
        return HomeworkClassService.addHomeworkClass(HomeworkClassBo);
    }

    public AjaxResult updateHomeworkClass(@Validated @RequestBody HomeworkClassBo HomeworkClassBo) {
        if (null == HomeworkClassBo.getOid()) {
            return AjaxResult.fail("oid不能为空");
        }
        return HomeworkClassService.updateHomeworkClass(HomeworkClassBo);
    }

    public AjaxResult<HomeworkClassVo> getDetail(@NotNull(message = "请选择数据") String oid) {
        return AjaxResult.success(HomeworkClassService.getDetail(oid));
    }

    public AjaxResult delete(@NotNull(message = "请选择需要删除的数据") String oid) {
            HomeworkClassBo HomeworkClassBo = new HomeworkClassBo();
            HomeworkClassBo.setOid(oid);
            HomeworkClassBo.setIsDelete(StatusEnum.ISDELETE.getCode());
        return HomeworkClassService.updateHomeworkClass(HomeworkClassBo);
    }
}
