package com.light.aiszzy.homework.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.light.aiszzy.homework.entity.dto.HomeworkClassDto;
import com.light.aiszzy.homework.entity.bo.HomeworkClassConditionBo;
import com.light.aiszzy.homework.entity.vo.HomeworkClassVo;

/**
 * 作业班级信息，包含疑问项汇总Mapper
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-22 17:01:24
 */
public interface HomeworkClassMapper extends BaseMapper<HomeworkClassDto> {

	List<HomeworkClassVo> getHomeworkClassListByCondition(HomeworkClassConditionBo condition);

}
