package com.light.enums;

/**
 * 批改结果枚举
 */
public enum CorrectResultEnum {

    CORRECT(1, "正确"),
    WRONG(2, "错误"),
    UNKNOWN(3, "未知");

    private final Integer code;

    private final String reason;

    CorrectResultEnum(Integer code, String reason) {
        this.code = code;
        this.reason = reason;
    }

    public Integer getCode() {
        return code;
    }

    public String getReason() {
        return reason;
    }

}
