package com.light.aiszzy.homework.entity.bo;



import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 作业班级信息，包含疑问项汇总
 * 
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-22 17:01:24
 */
@Data
public class HomeworkClassBo implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 作业班级信息，包含疑问项汇总id
	 */
	@ApiModelProperty("作业班级信息，包含疑问项汇总id")
	private Long HomeworkClassId;
	
	/**
	 * 主键
	 */
	@ApiModelProperty("主键")
	private Long id;
	/**
	 * oid
	 */
	@ApiModelProperty("oid")
	private String oid;
	/**
	 * 作业名称
	 */
	@ApiModelProperty("作业名称")
	private String homeworkOid;
	/**
	 * 学校id
	 */
	@ApiModelProperty("学校id")
	private String orgCode;
	/**
	 * 班级oid
	 */
	@ApiModelProperty("班级id")
	private Long classId;
	/**
	 * 学科code
	 */
	@ApiModelProperty("学科code")
	private Long subject;
	/**
	 * 学期  1:上学期  2：下学期
	 */
	@ApiModelProperty("学期  1:上学期  2：下学期")
	private Long term;
	/**
	 * 剩余疑问项数量
	 */
	@ApiModelProperty("剩余疑问项数量")
	private Long remainderDoubtCount;
	/**
	 * 创建时间
	 */
	@ApiModelProperty("创建时间")
	private Date createTime;
	/**
	 * 更新时间
	 */
	@ApiModelProperty("更新时间")
	private Date updateTime;
	/**
	 * 创建者
	 */
	@ApiModelProperty("创建者")
	private String createBy;
	/**
	 * 更新者
	 */
	@ApiModelProperty("更新者")
	private String updateBy;
	/**
	 * 是否删除，0：否，1：是
	 */
	@ApiModelProperty("是否删除，0：否，1：是")
	private Integer isDelete;

}
