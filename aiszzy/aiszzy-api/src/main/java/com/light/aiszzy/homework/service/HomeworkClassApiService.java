package com.light.aiszzy.homework.service;


import com.light.core.entity.AjaxResult;
import com.light.feign.interceptors.FeignClientInterceptor;
import com.light.aiszzy.homework.api.HomeworkClassApi;
import com.light.aiszzy.homework.entity.bo.HomeworkClassBo;
import com.light.aiszzy.homework.entity.bo.HomeworkClassConditionBo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;


/**
 * 作业班级信息，包含疑问项汇总接口
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-22 17:01:24
 */
@FeignClient(contextId = "HomeworkClassApiService", value="light-aiszzy" ,configuration = FeignClientInterceptor.class, fallbackFactory = HomeworkClassApiService.HomeworkClassApiFallbackFactory.class)
@Component
public interface HomeworkClassApiService  extends HomeworkClassApi {

	@Component
	class HomeworkClassApiFallbackFactory implements FallbackFactory<HomeworkClassApiService> {
		private static final Logger LOGGER = LoggerFactory.getLogger(HomeworkClassApiFallbackFactory.class);
		@Override
		public HomeworkClassApiService create(Throwable cause) {
			HomeworkClassApiFallbackFactory.LOGGER.error("用户服务调用失败:{}", cause.getMessage());
			return new HomeworkClassApiService() {
				public AjaxResult getHomeworkClassPageListByCondition(HomeworkClassConditionBo condition){
					return AjaxResult.fail("作业班级信息，包含疑问项汇总查询失败");
				}
				public AjaxResult getHomeworkClassListByCondition(HomeworkClassConditionBo condition){
					return AjaxResult.fail("作业班级信息，包含疑问项汇总查询失败");
				}

				public AjaxResult addHomeworkClass(HomeworkClassBo HomeworkClassBo){
					return AjaxResult.fail("作业班级信息，包含疑问项汇总新增失败");
				}

				public AjaxResult updateHomeworkClass(HomeworkClassBo HomeworkClassBo){
					return AjaxResult.fail("作业班级信息，包含疑问项汇总更新失败");
				}

				public AjaxResult getDetail(String oid){
					return AjaxResult.fail("作业班级信息，包含疑问项汇总获取详情失败");
				}

				public AjaxResult delete(String oid){
					return AjaxResult.fail("作业班级信息，包含疑问项汇总删除失败");
				}
			};
		}
	}
}