package com.light.aiszzy.homework.api;

import com.github.pagehelper.PageInfo;
import com.light.core.entity.AjaxResult;
import com.light.aiszzy.homework.entity.vo.HomeworkClassVo;
import com.light.aiszzy.homework.entity.bo.HomeworkClassBo;
import com.light.aiszzy.homework.entity.bo.HomeworkClassConditionBo;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import javax.validation.constraints.NotNull;
import java.util.List;


/**
 * 作业班级信息，包含疑问项汇总接口
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-22 17:01:24
 */
public interface HomeworkClassApi  {

	/**
	 * 查询作业班级信息，包含疑问项汇总列表
	 * <AUTHOR>
	 * @date 2025-07-22 17:01:24
	 */
	@PostMapping("/HomeworkClass/pageList")
	@ApiOperation(value = "分页查询作业班级信息，包含疑问项汇总",httpMethod = "POST")
	AjaxResult<PageInfo<HomeworkClassVo>> getHomeworkClassPageListByCondition(@RequestBody HomeworkClassConditionBo condition);

	/**
	 * 查询所有作业班级信息，包含疑问项汇总列表
	 * <AUTHOR>
	 * @date 2025-07-22 17:01:24
	 */
	@PostMapping("/HomeworkClass/list")
	@ApiOperation(value = "查询所有作业班级信息，包含疑问项汇总",httpMethod = "POST")
	AjaxResult<List<HomeworkClassVo>> getHomeworkClassListByCondition(@RequestBody HomeworkClassConditionBo condition);


	/**
	 * 新增作业班级信息，包含疑问项汇总
	 * <AUTHOR>
	 * @date 2025-07-22 17:01:24
	 */
	@PostMapping("/HomeworkClass/add")
	@ApiOperation(value = "新增作业班级信息，包含疑问项汇总",httpMethod = "POST")
	AjaxResult addHomeworkClass(@Validated @RequestBody HomeworkClassBo HomeworkClassBo);

	/**
	 * 修改作业班级信息，包含疑问项汇总
	 * @param HomeworkClassBo
	 * @return
	 * @returnType AjaxResult
	 * <AUTHOR>
	 * @date 2025-07-22 17:01:24
	 */
	@PostMapping("/HomeworkClass/update")
	@ApiOperation(value = "修改作业班级信息，包含疑问项汇总",httpMethod = "POST")
	AjaxResult updateHomeworkClass(@Validated @RequestBody HomeworkClassBo HomeworkClassBo);

	/**
	 * 查询作业班级信息，包含疑问项汇总详情
	 * @param oid
	 * @return
	 * @returnType AjaxResult
	 * <AUTHOR>
	 * @date 2025-07-22 17:01:24
	 */
	@GetMapping("/HomeworkClass/detail")
	@ApiOperation(value = "查询作业班级信息，包含疑问项汇总详情",httpMethod = "GET")
	AjaxResult<HomeworkClassVo> getDetail(@NotNull(message = "请选择数据") @RequestParam("oid") String oid);

	/**
	 * 删除作业班级信息，包含疑问项汇总
	 * @param oid
	 * @return
	 * @returnType AjaxResult
	 * <AUTHOR>
	 * @date 2025-07-22 17:01:24
	 */
	@GetMapping("/HomeworkClass/delete")
	@ApiOperation(value = "删除作业班级信息，包含疑问项汇总",httpMethod = "GET")
	@ApiImplicitParam(name = "id", value = "oid", required = true, dataType = "String", paramType = "delete")
	AjaxResult delete(@NotNull(message = "请选择需要删除的数据") @RequestParam("oid") String oid);



}

