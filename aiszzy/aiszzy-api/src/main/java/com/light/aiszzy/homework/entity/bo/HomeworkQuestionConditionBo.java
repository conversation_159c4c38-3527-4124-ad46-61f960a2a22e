package com.light.aiszzy.homework.entity.bo;

import com.light.core.entity.PageLimitBo;

import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import lombok.Data;

/**
 * 校本作业题目信息
 * 
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-10 13:50:38
 */
@Data
public class HomeworkQuestionConditionBo extends PageLimitBo{

	/**
	 * 题目id
	 */
	@ApiModelProperty("题目id")
	private Long id;

	/**
	 * oid
	 */
	@ApiModelProperty("oid")
	private String oid;

	/**
	 * 作业id
	 */
	@ApiModelProperty("作业id")
	private String homeworkOid;

	/**
	 * 关联question题目oid
	 */
	@ApiModelProperty("关联question题目oid")
	private String questionOid;

	/**
	 * 内容(原，包含学生作答区)
	 */
	@ApiModelProperty("内容(原，包含学生作答区)")
	private String content;

	/**
	 * 内容(不包含学生作答区)
	 */
	@ApiModelProperty("内容(不包含学生作答区)")
	private String smallContent;

	/**
	 * 题目文本内容
	 */
	@ApiModelProperty("题目文本内容")
	private String textContent;

	/**
	 * 位置(原，包含学生作答区)
	 */
	@ApiModelProperty("位置(原，包含学生作答区)")
	private String position;

	/**
	 * 位置(不包含学生作答区)
	 */
	@ApiModelProperty("位置(不包含学生作答区)")
	private String smallPosition;

	/**
	 * 页码
	 */
	@ApiModelProperty("页码")
	private Long pageNoSearch;

	/**
	 * 题号
	 */
	@ApiModelProperty("题号")
	private String questionNo;

	/**
	 * 大题号标题
	 */
	@ApiModelProperty("大题号标题")
	private String bigNumTitle;

	/**
	 * 小题号标题
	 */
	@ApiModelProperty("小题号标题")
	private String smallNumTitle;


	/**
	 * 题型
	 */
	@ApiModelProperty("题型id")
	private String questionTypeId;

	/**
	 * 题型名称
	 */
	@ApiModelProperty("题型名称")
	private String questionTypeName;

	/**
	 * 章节ID
	 */
	@ApiModelProperty("章节ID")
	private String chapterId;

	/**
	 * 节ID
	 */
	@ApiModelProperty("节ID")
	private String sectionId;

	/**
	 * 知识点，多个
	 */
	@ApiModelProperty("知识点，多个")
	private String knowledgePointsId;

	/**
	 * 学科code
	 */
	@ApiModelProperty("学科code")
	private Long subject;

	/**
	 * 年级code
	 */
	@ApiModelProperty("年级code")
	private Integer grade;

	/**
	 * 难度
	 */
	@ApiModelProperty("难度")
	private Integer difficultId;

	/**
	 * 启用年份
	 */
	@ApiModelProperty("启用年份")
	private String year;

	/**
	 * 题目排序
	 */
	@ApiModelProperty("题目排序")
	private Long quesOrderNum;

	/**
	 * 题型排序
	 */
	@ApiModelProperty("题型排序")
	private Long typeOrderNum;

	/**
	 * 题目url或文字
	 */
	@ApiModelProperty("题目url或文字")
	private String quesBody;

	/**
	 * 公共题干url或文字
	 */
	@ApiModelProperty("公共题干url或文字")
	private String publicQues;

	/**
	 * 答案url或文字
	 */
	@ApiModelProperty("答案url或文字")
	private String quesAnswer;

	/**
	 * 解析url或文字
	 */
	@ApiModelProperty("解析url或文字")
	private String analysisAnswer;

	/**
	 * 题目展示类型  0：图片url  1：html文字 
	 */
	@ApiModelProperty("题目展示类型  0：图片url  1：html文字 ")
	private Long quesBodyType;

	/**
	 * 公共题干展示类型  0：图片url  1：html文字
	 */
	@ApiModelProperty("公共题干展示类型  0：图片url  1：html文字")
	private Long publicQuesType;

	/**
	 * 答案展示类型  0：图片url  1：html文字 
	 */
	@ApiModelProperty("答案展示类型  0：图片url  1：html文字 ")
	private Long quesAnswerType;

	/**
	 * 解析展示类型  0：图片url  1：html文字
	 */
	@ApiModelProperty("解析展示类型  0：图片url  1：html文字 ")
	private Long analysisAnswerType;

	/**
	 * 相似题json，解析后question_oid存入json
	 */
	@ApiModelProperty("相似题json，解析后question_oid存入json")
	private String similarRecommendResult;

	/**
	 * 创建时间
	 */
	@ApiModelProperty("创建时间")
	private Date createTime;

	/**
	 * 更新时间
	 */
	@ApiModelProperty("更新时间")
	private Date updateTime;

	/**
	 * 创建者
	 */
	@ApiModelProperty("创建者")
	private String createBy;

	/**
	 * 更新者
	 */
	@ApiModelProperty("更新者")
	private String updateBy;

	/**
	 * 是否删除，0：否，1：是
	 */
	@ApiModelProperty("是否删除，0：否，1：是")
	private Integer isDelete;

}
